/**
 * Centralized Icon Exports
 * All Hero Icons used in the customer support app
 * Based on Figma design specifications
 */

// Outline Icons (default)
export {
  DocumentChartBarIcon,
  MagnifyingGlassIcon,
  Cog6ToothIcon,
  BellIcon,
  ArrowRightStartOnRectangleIcon,
  PhoneIcon,
  EnvelopeIcon,
  UserGroupIcon,
  UserIcon,
  UserCircleIcon,
  PencilSquareIcon,
  PaperClipIcon,
  Squares2X2Icon,
  TicketIcon,
  IdentificationIcon,
  XCircleIcon,
  CheckCircleIcon,
  InformationCircleIcon,
  ArrowLeftIcon,
  EllipsisHorizontalIcon,
  XMarkIcon,
  FunnelIcon,
  FaceSmileIcon,
  MapPinIcon,
  ArrowsUpDownIcon,
  ChevronDoubleRightIcon,
  ChevronLeftIcon,
} from '@heroicons/react/24/outline';

// Solid Icons (for filled variants)
export {
  DocumentChartBarIcon as DocumentChartBarIconSolid,
  MagnifyingGlassIcon as MagnifyingGlassIconSolid,
  Cog6ToothIcon as Cog6ToothIconSolid,
  BellIcon as BellIconSolid,
  ArrowRightStartOnRectangleIcon as ArrowRightStartOnRectangleIconSolid,
  PhoneIcon as PhoneIconSolid,
  EnvelopeIcon as EnvelopeIconSolid,
  UserGroupIcon as UserGroupIconSolid,
  UserIcon as UserIconSolid,
  UserCircleIcon as UserCircleIconSolid,
  PencilSquareIcon as PencilSquareIconSolid,
  PaperClipIcon as PaperClipIconSolid,
  Squares2X2Icon as Squares2X2IconSolid,
  TicketIcon as TicketIconSolid,
  IdentificationIcon as IdentificationIconSolid,
  XCircleIcon as XCircleIconSolid,
  CheckCircleIcon as CheckCircleIconSolid,
  InformationCircleIcon as InformationCircleIconSolid,
  ArrowLeftIcon as ArrowLeftIconSolid,
  EllipsisHorizontalIcon as EllipsisHorizontalIconSolid,
  XMarkIcon as XMarkIconSolid,
  FunnelIcon as FunnelIconSolid,
  FaceSmileIcon as FaceSmileIconSolid,
  MapPinIcon as MapPinIconSolid,
  ArrowsUpDownIcon as ArrowsUpDownIconSolid,
  ChevronDoubleRightIcon as ChevronDoubleRightIconSolid,
  ChevronLeftIcon as ChevronLeftIconSolid,
} from '@heroicons/react/24/solid';

// Icon mapping for easy access by name
export const ICONS = {
  // Outline variants
  'document-chart-bar': DocumentChartBarIcon,
  'magnifying-glass': MagnifyingGlassIcon,
  'cog-6-tooth': Cog6ToothIcon,
  'bell': BellIcon,
  'arrow-right-start-on-rectangle': ArrowRightStartOnRectangleIcon,
  'phone': PhoneIcon,
  'envelope': EnvelopeIcon,
  'user-group': UserGroupIcon,
  'user': UserIcon,
  'user-circle': UserCircleIcon,
  'pencil-square': PencilSquareIcon,
  'paper-clip': PaperClipIcon,
  'squares-2x2': Squares2X2Icon,
  'ticket': TicketIcon,
  'identification': IdentificationIcon,
  'x-circle': XCircleIcon,
  'check-circle': CheckCircleIcon,
  'information-circle': InformationCircleIcon,
  'arrow-left': ArrowLeftIcon,
  'ellipsis-horizontal': EllipsisHorizontalIcon,
  'x-mark': XMarkIcon,
  'funnel': FunnelIcon,
  'face-smile': FaceSmileIcon,
  'map-pin': MapPinIcon,
  'arrows-up-down': ArrowsUpDownIcon,
  'chevron-double-right': ChevronDoubleRightIcon,
  'chevron-left': ChevronLeftIcon,
};

// Solid icon mapping
export const ICONS_SOLID = {
  'document-chart-bar': DocumentChartBarIconSolid,
  'magnifying-glass': MagnifyingGlassIconSolid,
  'cog-6-tooth': Cog6ToothIconSolid,
  'bell': BellIconSolid,
  'arrow-right-start-on-rectangle': ArrowRightStartOnRectangleIconSolid,
  'phone': PhoneIconSolid,
  'envelope': EnvelopeIconSolid,
  'user-group': UserGroupIconSolid,
  'user': UserIconSolid,
  'user-circle': UserCircleIconSolid,
  'pencil-square': PencilSquareIconSolid,
  'paper-clip': PaperClipIconSolid,
  'squares-2x2': Squares2X2IconSolid,
  'ticket': TicketIconSolid,
  'identification': IdentificationIconSolid,
  'x-circle': XCircleIconSolid,
  'check-circle': CheckCircleIconSolid,
  'information-circle': InformationCircleIconSolid,
  'arrow-left': ArrowLeftIconSolid,
  'ellipsis-horizontal': EllipsisHorizontalIconSolid,
  'x-mark': XMarkIconSolid,
  'funnel': FunnelIconSolid,
  'face-smile': FaceSmileIconSolid,
  'map-pin': MapPinIconSolid,
  'arrows-up-down': ArrowsUpDownIconSolid,
  'chevron-double-right': ChevronDoubleRightIconSolid,
  'chevron-left': ChevronLeftIconSolid,
};
